<script>
	//src/routes/+page.svelte
	import { fade } from 'svelte/transition';
	import { onMount } from 'svelte';
	import { page } from '$app/stores';
	import NavbarDesktop from '$lib/components/sections/navbar/NavbarDesktop.svelte';
	import NavbarMobile from '$lib/components/sections/navbar/NavbarMobile.svelte';
	import { Skeleton } from '$lib/components/ui/skeleton';
	import { browser } from '$app/environment';
	import { userStore } from '$lib/stores/userLogin.js';
	import MoreModal from '$lib/components/sections/shared/MoreModal.svelte';
	import ExportModal from '$lib/components/sections/shared/ExportModal.svelte';

	export let data;
	$: isLoggedIn = $userStore ? $userStore.role === 'authenticated' : false;

	let showNavbar = false;
	let mounted = false;
	let isMobileLandscape = false;
	let componentsLoaded = {
		hero: false,
		newReleases: false,
		continueWatching: false,
		upcomingEpisodes: false,
		popularNow: false,
		releaseSchedule: false,
		footer: false
	};

	// Add state for the modals
	let showStremioModal = false;
	let showExportModal = false;

	// Check for export tutorial parameter immediately
	if (browser && $page.url.searchParams.has('showExportTutorial')) {
		showExportModal = true;
	}

	function handleCloseStremioModal() {
		showStremioModal = false;
	}

	function handleCloseExportModal() {
		showExportModal = false;
	}

	let HeroContainer;
	let NewReleasesContainer;
	let ContinueWatchingContainer;
	let UpcomingEpisodesContainer;
	let PopularNowContainer;
	let ReleaseScheduleContainer;
	let Footer;

	function checkMobileLandscape() {
		// Disable landscape prompt on home screen
		isMobileLandscape = false;
	}

	onMount(async () => {
		mounted = true;
		if ($page.state && $page.state.scrollY) {
			setTimeout(() => {
				window.scrollTo({
					top: $page.state.scrollY
				});
			}, 0);
		}
		checkMobileLandscape();
		window.addEventListener('resize', checkMobileLandscape);
		await loadComponents();

		return () => {
			window.removeEventListener('resize', checkMobileLandscape);
		};
	});

	$: if (mounted) {
		if (isMobileLandscape) {
			document.body.classList.add('landscape-overlay');
		} else {
			document.body.classList.remove('landscape-overlay');
		}
	}

	async function loadComponents() {
		const components = [
			{ name: 'hero', loader: () => import('$lib/components/containers/homepage/HeroContainer.svelte') },
			{ name: 'newReleases', loader: () => import('$lib/components/containers/homepage/NewReleasesContainer.svelte') },
			{ name: 'continueWatching', loader: () => import('$lib/components/containers/homepage/ContinueWatchingContainer.svelte') },
			{ name: 'upcomingEpisodes', loader: () => import('$lib/components/containers/homepage/UpcomingEpisodesContainer.svelte') },
			{ name: 'popularNow', loader: () => import('$lib/components/containers/homepage/PopularNowContainer.svelte') },
			{ name: 'releaseSchedule', loader: () => import('$lib/components/containers/homepage/ReleaseScheduleContainer.svelte') },
			{ name: 'footer', loader: () => import('$lib/components/sections/shared/Footer.svelte') }
		];

		for (const component of components) {
			const module = await component.loader();
			switch (component.name) {
				case 'hero':
					HeroContainer = module.default;
					break;
				case 'newReleases':
					NewReleasesContainer = module.default;
					break;
				case 'continueWatching':
					ContinueWatchingContainer = module.default;
					break;
				case 'upcomingEpisodes':
					UpcomingEpisodesContainer = module.default;
					break;
				case 'popularNow':
					PopularNowContainer = module.default;
					break;
				case 'releaseSchedule':
					ReleaseScheduleContainer = module.default;
					break;
				case 'footer':
					Footer = module.default;
					break;
			}
			componentsLoaded[component.name] = true;
		}
	}

	function hasContinueWatching(data) {
		return data?.continueWatchingData?.length > 0;
	}

	// Reactive title and description variables
	$: title = 'Strona Główna - Lycoris';
	$: description = 'Oglądaj anime online za darmo, śledź postępy oglądania i dołącz do społeczności fanów anime na lycoris.cafe';
	$: imageUrl = 'https://pixeldrain.com/api/file/nzfyjq8f';
	$: canonicalUrl = browser ? window.location.href : 'https://lycoris.cafe';
</script>

<svelte:head>
	<!-- Basic metadata -->
	<title>{title}</title>
	<meta name="description" content={description} />

	<!-- OpenGraph tags -->
	<meta name="twitter:title" content={title} />
	<meta property="og:description" content={description} />
	<meta property="og:image" content={imageUrl} />
	<meta property="og:image:width" content="1200" />
	<meta property="og:image:height" content="630" />
	<meta property="og:image:alt" content="Lycoris.cafe - Strona Główna" />
	<meta property="og:url" content={canonicalUrl} />
	<meta property="og:type" content="website" />
	<meta property="og:site_name" content="Lycoris" />

	<!-- Twitter Card tags -->
	<meta name="twitter:card" content="summary_large_image" />
	<meta name="twitter:site" content="@lycoris_cafe" />
	<meta name="twitter:title" content={title} />
	<meta name="twitter:description" content={description} />
	<meta name="twitter:image" content={imageUrl} />
	<meta name="twitter:image:alt" content="Lycoris.cafe - Strona Główna" />

	<!-- Canonical URL -->
	<link rel="canonical" href={canonicalUrl} />

	<!-- Additional SEO metadata -->
	<meta name="keywords" content="anime, streaming, anime online, lycoris, lycoris cafe, anime community" />
	<meta name="theme-color" content="#ee8585" />
	<meta name="author" content="Lycoris" />
	<meta name="robots" content="index, follow" />
</svelte:head>

<div>
	<main class="overflow-x-hidden bg-gray-900 opacity-100 md:ml-16" id="main">
		{#if isMobileLandscape}
			<div class="fixed inset-0 z-9999 flex items-center justify-center bg-gray-900/95 backdrop-blur-xs" style="touch-action: none;" on:touchmove|preventDefault on:wheel|preventDefault>
				<div class="max-w-[85%] rounded-xl border border-white/15 bg-gray-800/60 p-6 text-center backdrop-blur-xs">
					<svg class="mx-auto mb-4 h-12 w-12 text-yellow-400" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24" stroke="currentColor">
						<path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 19l9 2-9-18-9 18 9-2zm0 0v-8" />
					</svg>
					<h2 class="mb-2 text-xl font-bold text-white">Obróć swoje urządzenie</h2>
					<p class="text-gray-300">Ta strona działa najlepiej w trybie pionowym. Proszę obróć swoje urządzenie.</p>
				</div>
			</div>
		{/if}

		<NavbarDesktop on:showStremioModal={() => (showStremioModal = true)} />
		<NavbarMobile />

		{#await data.streamed.homeData}
			<div class="relative h-[400px] w-full md:h-[60vh]">
				<Skeleton class="h-full w-full" />
				<div class="absolute right-0 bottom-0 left-0 bg-linear-to-t from-gray-900 via-gray-900/60 to-transparent p-4" />
			</div>

			<div class="mt-20 mb-20">
				<div class="w-full">
					<div class="px-4">
						<Skeleton class="mb-4 h-8 w-1/4" />
					</div>
					<div class="carousel-container overflow-hidden">
						<div class="carousel flex space-x-4 px-4">
							{#each Array(10) as _}
								<div class="carousel-slide shrink-0">
									<div class="w-[190px] overflow-hidden rounded-lg bg-gray-800/50 transition-all duration-300 sm:w-[230px] md:w-[240px] lg:w-[300px]">
										<div class="relative w-full" style="aspect-ratio: 12 / 5;">
											<Skeleton class="h-full w-full rounded-t-lg" />
										</div>

										<div class="space-y-1 px-2 py-3">
											<Skeleton class="mb-2 h-4 w-[85%]" />
											<Skeleton class="mb-2 hidden h-3 w-[70%] md:block" />
											<div class="flex items-center justify-between">
												<Skeleton class="h-3 w-[45%]" />
												<Skeleton class="h-3 w-[25%]" />
											</div>
											<div class="hidden items-center justify-between md:flex">
												<Skeleton class="h-3 w-[35%]" />
												<Skeleton class="h-3 w-[30%]" />
											</div>
										</div>
									</div>
								</div>
							{/each}
						</div>
					</div>
				</div>
			</div>

			<div class="mb-20">
				<div class="w-full">
					<div class="px-4">
						<Skeleton class="mb-4 h-8 w-1/4" />
					</div>
					<div class="carousel-container overflow-hidden">
						<div class="carousel flex space-x-4 px-4">
							{#each Array(10) as _}
								<div class="carousel-slide shrink-0">
									<div class="w-[190px] overflow-hidden rounded-lg bg-gray-800/50 transition-all duration-300 sm:w-[230px] md:w-[240px] lg:w-[300px]">
										<div class="relative w-full" style="aspect-ratio: 12 / 5;">
											<Skeleton class="h-full w-full rounded-t-lg" />
										</div>

										<div class="space-y-1 px-2 py-3">
											<Skeleton class="mb-2 h-4 w-[85%]" />
											<Skeleton class="mb-2 hidden h-3 w-[70%] md:block" />
											<div class="flex items-center justify-between">
												<Skeleton class="h-3 w-[45%]" />
												<Skeleton class="h-3 w-[25%]" />
											</div>
											<div class="hidden items-center justify-between md:flex">
												<Skeleton class="h-3 w-[35%]" />
												<Skeleton class="h-3 w-[30%]" />
											</div>
										</div>
									</div>
								</div>
							{/each}
						</div>
					</div>
				</div>
			</div>
		{:then pageData}
			{#if isLoggedIn === true}
				{#if componentsLoaded.hero}
					<svelte:component this={HeroContainer} data={pageData} userSettings={data.userSettings} />
				{:else}
					<div class="relative h-[400px] w-full md:h-[60vh]">
						<Skeleton class="h-full w-full" />
						<div class="absolute right-0 bottom-0 left-0 bg-linear-to-t from-gray-900 via-gray-900/60 to-transparent p-4" />
					</div>
				{/if}

				<div class="mt-20 mb-20">
					{#if componentsLoaded.newReleases}
						<svelte:component this={NewReleasesContainer} data={pageData} userSettings={data.userSettings} />
					{:else}
						<div class="w-full">
							<div class="px-4">
								<Skeleton class="mb-4 h-8 w-1/4" />
							</div>
							<div class="carousel-container overflow-hidden">
								<div class="carousel flex space-x-4 px-4">
									{#each Array(10) as _}
										<div class="carousel-slide shrink-0">
											<div class="w-[190px] overflow-hidden rounded-lg bg-gray-800/50 transition-all duration-300 sm:w-[230px] md:w-[240px] lg:w-[300px]">
												<div class="relative w-full" style="aspect-ratio: 12 / 5;">
													<Skeleton class="h-full w-full rounded-t-lg" />
												</div>

												<div class="space-y-1 px-2 py-3">
													<Skeleton class="mb-2 h-4 w-[85%]" />
													<Skeleton class="mb-2 hidden h-3 w-[70%] md:block" />
													<div class="flex items-center justify-between">
														<Skeleton class="h-3 w-[45%]" />
														<Skeleton class="h-3 w-[25%]" />
													</div>
													<div class="hidden items-center justify-between md:flex">
														<Skeleton class="h-3 w-[35%]" />
														<Skeleton class="h-3 w-[30%]" />
													</div>
												</div>
											</div>
										</div>
									{/each}
								</div>
							</div>
						</div>
					{/if}
				</div>

				{#if hasContinueWatching(pageData)}
					{#if componentsLoaded.continueWatching}
						<svelte:component this={ContinueWatchingContainer} data={pageData} userSettings={data.userSettings} />
					{:else}
						<div class="mb-20 w-full">
							<div class="px-4">
								<Skeleton class="mb-4 h-8 w-1/4" />
							</div>
							<div class="carousel-container overflow-hidden">
								<div class="carousel flex space-x-4 px-4">
									{#each Array(10) as _}
										<div class="carousel-slide shrink-0">
											<div class="w-[190px] overflow-hidden rounded-lg bg-gray-800/50 transition-all duration-300 sm:w-[230px] md:w-[240px] lg:w-[300px]">
												<div class="relative w-full" style="aspect-ratio: 12 / 5;">
													<Skeleton class="h-full w-full rounded-t-lg" />
												</div>

												<div class="space-y-1 px-2 py-3">
													<Skeleton class="mb-2 h-4 w-[85%]" />
													<Skeleton class="mb-2 hidden h-3 w-[70%] md:block" />
													<div class="flex items-center justify-between">
														<Skeleton class="h-3 w-[45%]" />
														<Skeleton class="h-3 w-[25%]" />
													</div>
													<div class="hidden items-center justify-between md:flex">
														<Skeleton class="h-3 w-[35%]" />
														<Skeleton class="h-3 w-[30%]" />
													</div>
												</div>
											</div>
										</div>
									{/each}
								</div>
							</div>
						</div>
					{/if}
				{/if}

				{#if componentsLoaded.upcomingEpisodes}
					<svelte:component this={UpcomingEpisodesContainer} data={pageData} userSettings={data.userSettings} />
				{:else}
					<div class="mb-20 w-full">
						<div class="px-4">
							<Skeleton class="mb-4 h-8 w-1/4" />
						</div>
						<div class="carousel-container overflow-hidden">
							<div class="carousel flex space-x-4 px-4">
								{#each Array(10) as _}
									<div class="carousel-slide shrink-0">
										<div class="w-48 md:w-56 lg:w-64">
											<Skeleton class="mb-2 aspect-video w-full" />
											<Skeleton class="mb-1 h-4 w-3/4" />
											<Skeleton class="h-3 w-1/2" />
										</div>
									</div>
								{/each}
							</div>
						</div>
					</div>
				{/if}

				{#if componentsLoaded.popularNow}
					<svelte:component this={PopularNowContainer} data={pageData} userSettings={data.userSettings} />
				{/if}

				{#if componentsLoaded.releaseSchedule}
					<svelte:component this={ReleaseScheduleContainer} data={pageData} userSettings={data.userSettings} />
				{/if}

				{#if componentsLoaded.footer}
					<svelte:component this={Footer} userSettings={data.userSettings} />
				{/if}
			{:else}
				<!-- Not logged in content -->
				{#if componentsLoaded.hero}
					<svelte:component this={HeroContainer} data={pageData} userSettings={data.userSettings} />
				{:else}
					<div class="relative h-[400px] w-full md:h-[60vh]">
						<Skeleton class="h-full w-full" />
						<div class="absolute right-0 bottom-0 left-0 bg-linear-to-t from-gray-900 via-gray-900/60 to-transparent p-4" />
					</div>
				{/if}

				<div class="mt-20 mb-20">
					{#if componentsLoaded.newReleases}
						<svelte:component this={NewReleasesContainer} data={pageData} userSettings={data.userSettings} />
					{:else}
						<div class="w-full">
							<div class="px-4">
								<Skeleton class="mb-4 h-8 w-1/4" />
							</div>
							<div class="carousel-container overflow-hidden">
								<div class="carousel flex space-x-4 px-4">
									{#each Array(10) as _}
										<div class="carousel-slide shrink-0">
											<div class="w-[190px] overflow-hidden rounded-lg bg-gray-800/50 transition-all duration-300 sm:w-[230px] md:w-[240px] lg:w-[300px]">
												<div class="relative w-full" style="aspect-ratio: 12 / 5;">
													<Skeleton class="h-full w-full rounded-t-lg" />
												</div>

												<div class="space-y-1 px-2 py-3">
													<Skeleton class="mb-2 h-4 w-[85%]" />
													<Skeleton class="mb-2 hidden h-3 w-[70%] md:block" />
													<div class="flex items-center justify-between">
														<Skeleton class="h-3 w-[45%]" />
														<Skeleton class="h-3 w-[25%]" />
													</div>
													<div class="hidden items-center justify-between md:flex">
														<Skeleton class="h-3 w-[35%]" />
														<Skeleton class="h-3 w-[30%]" />
													</div>
												</div>
											</div>
										</div>
									{/each}
								</div>
							</div>
						</div>
					{/if}
				</div>

				{#if componentsLoaded.upcomingEpisodes}
					<svelte:component this={UpcomingEpisodesContainer} data={pageData} userSettings={data.userSettings} />
				{:else}
					<div class="mb-20 w-full">
						<div class="px-4">
							<Skeleton class="mb-4 h-8 w-1/4" />
						</div>
						<div class="carousel-container overflow-hidden">
							<div class="carousel flex space-x-4 px-4">
								{#each Array(10) as _}
									<div class="carousel-slide shrink-0">
										<div class="w-[190px] overflow-hidden rounded-lg bg-gray-800/50 transition-all duration-300 sm:w-[230px] md:w-[240px] lg:w-[300px]">
											<div class="relative w-full" style="aspect-ratio: 12 / 5;">
												<Skeleton class="h-full w-full rounded-t-lg" />
											</div>

											<div class="space-y-1 px-2 py-3">
												<Skeleton class="mb-2 h-4 w-[85%]" />
												<Skeleton class="mb-2 hidden h-3 w-[70%] md:block" />
												<div class="flex items-center justify-between">
													<Skeleton class="h-3 w-[45%]" />
													<Skeleton class="h-3 w-[25%]" />
												</div>
												<div class="hidden items-center justify-between md:flex">
													<Skeleton class="h-3 w-[35%]" />
													<Skeleton class="h-3 w-[30%]" />
												</div>
											</div>
										</div>
									</div>
								{/each}
							</div>
						</div>
					</div>
				{/if}

				{#if componentsLoaded.popularNow}
					<svelte:component this={PopularNowContainer} data={pageData} userSettings={data.userSettings} />
				{/if}

				{#if componentsLoaded.releaseSchedule}
					<svelte:component this={ReleaseScheduleContainer} data={pageData} userSettings={data.userSettings} />
				{/if}

				{#if componentsLoaded.continueWatching}
					<svelte:component this={ContinueWatchingContainer} data={pageData} userSettings={data.userSettings} />
				{/if}

				{#if componentsLoaded.footer}
					<svelte:component this={Footer} userSettings={data.userSettings} />
				{/if}
			{/if}
		{/await}
	</main>
</div>

{#if showStremioModal}
	<MoreModal {handleCloseStremioModal} />
{/if}

{#if showExportModal}
	<ExportModal bind:open={showExportModal} onClose={handleCloseExportModal} />
{/if}
