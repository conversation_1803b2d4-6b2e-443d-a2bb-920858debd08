<script>
	import { onMount, afterUpdate } from 'svelte';
	import ContinueWatching from '$lib/components/sections/homepage/ContinueWatching.svelte';
	import { tweened, spring } from 'svelte/motion';
	import { linear } from 'svelte/easing';
	import { ChevronDown, ChevronUp } from 'lucide-svelte';
	import { fade } from 'svelte/transition';
	import { userStore } from '$lib/stores/userLogin.js';
	import { progressStore } from '$lib/stores/progressStore';
	import UserProfileModal from '$lib/components/sections/shared/UserProfileModal.svelte';
	import { cacheKeys } from '$lib/utils/cacheUtils';
	import settingsStore from '$lib/stores/settingsStore';
	$: isLoggedIn = $userStore ? $userStore.role === 'authenticated' : false;
	$: provider = $userStore?.user_metadata?.provider || 'anilist';
	$: isAniList = provider === 'anilist';
	$: isMAL = provider === 'mal';
	$: filterCompletedShows = $settingsStore.filterCompletedShows;

	// Add this state variable with the other state variables
	let showUserModal = false;

	// Add these functions
	function handleLoginPrompt() {
		showUserModal = true;
	}

	function handleCloseModal() {
		showUserModal = false;
	}

	// Get data from parent
	export let data, userSettings;
	let preferRomaji;
	if (!userSettings?.titleLanguage) {
		preferRomaji = true;
	} else {
		preferRomaji = userSettings.titleLanguage === 'romaji';
	}

	const FADE_DURATION = 250;
	let listenersInitialized = false;
	let initialRender = true;
	// Use pre-merged continue watching data
	$: continueWatching = data.continueWatchingData || [];

	$: {
		if (continueWatching.length > 0) {
			// Standardize fields, filter out entries without images, and apply new sorting logic
			continueWatching = continueWatching
				.map((show) => {
					// Ensure fields are consistently named
					const current_episode = show.current_episode || show.num_episodes_watched || 0;
					const total_episodes = show.total_episodes || show.num_episodes || 0;
					const released_episodes = show.released_episodes || show.num_episodes || current_episode || 0;

					// Calculate unwatched episodes
					const unwatched_episodes = Math.max(0, released_episodes - current_episode);

					// Determine if show has new episodes (is airing and has unwatched episodes)
					const has_new_episodes = current_episode < released_episodes && released_episodes !== total_episodes;

					return {
						...show,
						current_episode,
						total_episodes,
						released_episodes,
						unwatched_episodes,
						has_new_episodes
					};
				})
				// Filter out entries that don't have an image
				.filter((show) => show.image)
				// Filter out shows that are fully up-to-date (no unwatched episodes) only if the setting is enabled
				.filter((show) => !filterCompletedShows || show.unwatched_episodes > 0)
				// Sort by:
				// 1. Shows with new episodes (airing + unwatched) first, sorted by unwatched count (low to high)
				// 2. Then shows with just unwatched episodes (not airing new ones), sorted by unwatched count (low to high)
				.sort((a, b) => {
					// First prioritize shows with new episodes (airing + unwatched)
					if (a.has_new_episodes && !b.has_new_episodes) return -1;
					if (!a.has_new_episodes && b.has_new_episodes) return 1;

					// Then sort by unwatched episode count (low to high)
					return a.unwatched_episodes - b.unwatched_episodes;
				});
		}
	}

	let containerRef;
	let carouselRef;
	let isVerticalView = false;
	let containerHeight = 0;
	let isDragging = false;
	let startX;
	let startScrollLeft;
	let lastScrollLeft = 0;
	let lastMouseX = 0;
	let velocityX = 0;
	let lastTimestamp = 0;
	let animationFrameId = null;
	let maxScroll = 0;
	let dragDistance = 0;
	let isOverscrolling = false;
	let isFading = false;
	let startY;
	let initialTouchY;
	let isVerticalScroll = false;

	const DRAG_THRESHOLD = 5;
	const rows = 3;
	let itemWidth = 240;
	let itemHeight = 140;

	const containerHeightTweened = tweened(0, {
		duration: initialRender ? 0 : 400,
		easing: linear
	});

	$: if (!initialRender) {
		if (isVerticalView) {
			containerHeightTweened.set(containerHeight);
		} else {
			containerHeightTweened.set(containerHeight / 1.5);
		}
	}

	const leftOverscroll = spring(0, {
		stiffness: 0.1,
		damping: 0.5
	});

	const rightOverscroll = spring(0, {
		stiffness: 1,
		damping: 0.5
	});

	function initializeEventListeners() {
		if (listenersInitialized) return;

		window.addEventListener('mousemove', handleDragMove);
		window.addEventListener('mouseup', handleDragEnd);
		window.addEventListener('touchmove', handleDragMove, { passive: false });
		window.addEventListener('touchend', handleDragEnd);

		listenersInitialized = true;
	}

	function getEventCoords(e) {
		return {
			x: e.type.includes('mouse') ? e.pageX : e.touches[0].pageX,
			y: e.type.includes('mouse') ? e.pageY : e.touches[0].pageY
		};
	}

	function handleDragStart(e) {
		if (!carouselRef || isVerticalView) return;

		isDragging = true;
		const coords = getEventCoords(e);
		startX = coords.x;
		startY = coords.y;
		initialTouchY = coords.y;
		startScrollLeft = carouselRef.scrollLeft || 0;
		lastMouseX = startX;
		lastTimestamp = Date.now();
		velocityX = 0;
		dragDistance = 0;
		isVerticalScroll = false;

		if (animationFrameId) {
			cancelAnimationFrame(animationFrameId);
		}

		if (carouselRef) {
			carouselRef.style.scrollBehavior = 'auto';
			carouselRef.style.cursor = 'grabbing';
		}
	}

	function handleDragMove(e) {
		if (!isDragging || !carouselRef || isVerticalView) return;

		const coords = getEventCoords(e);
		const deltaX = Math.abs(coords.x - startX);
		const deltaY = Math.abs(coords.y - initialTouchY);

		if (!isVerticalScroll && deltaY > deltaX && deltaY > 10) {
			isVerticalScroll = true;
			handleDragEnd(e);
			return;
		}

		if (isVerticalScroll) return;

		if (deltaX > deltaY) {
			e.preventDefault();
		}

		const currentTimestamp = Date.now();
		const timeElapsed = currentTimestamp - lastTimestamp;

		const moveX = coords.x - lastMouseX;
		dragDistance += Math.abs(moveX);

		if (timeElapsed > 0) {
			velocityX = moveX / timeElapsed;
		}

		const walkX = startX - coords.x;
		let newScrollLeft = startScrollLeft + walkX;

		if (newScrollLeft < 0) {
			isOverscrolling = true;
			leftOverscroll.set(-newScrollLeft * 0.3);
			rightOverscroll.set(0);
			newScrollLeft = 0;
		} else if (newScrollLeft > maxScroll) {
			isOverscrolling = true;
			rightOverscroll.set((newScrollLeft - maxScroll) * 0.3);
			leftOverscroll.set(0);
			newScrollLeft = maxScroll;
		} else {
			isOverscrolling = false;
			leftOverscroll.set(0);
			rightOverscroll.set(0);
		}

		if (carouselRef) {
			carouselRef.scrollLeft = newScrollLeft;
			lastScrollLeft = newScrollLeft;
		}

		lastMouseX = coords.x;
		lastTimestamp = currentTimestamp;
	}

	function handleDragEnd(e) {
		if (!isDragging || !carouselRef || isVerticalView) return;
		isDragging = false;

		if (carouselRef) {
			carouselRef.style.cursor = 'grab';
		}

		if (isOverscrolling) {
			isOverscrolling = false;
			leftOverscroll.set(0, { hard: false });
			rightOverscroll.set(0, { hard: false });
		}

		if (dragDistance > DRAG_THRESHOLD) {
			e.preventDefault();
			e.stopPropagation();

			const clickPreventionHandler = (e) => {
				e.preventDefault();
				e.stopPropagation();
			};

			carouselRef.addEventListener('click', clickPreventionHandler, { capture: true });
			setTimeout(() => {
				if (carouselRef) {
					carouselRef.removeEventListener('click', clickPreventionHandler, { capture: true });
				}
			}, 100);
		}

		if (carouselRef) {
			if (Math.abs(velocityX) > 0.1) {
				applyMomentumScroll();
			} else {
				carouselRef.style.scrollBehavior = 'smooth';
			}
		}
	}

	function applyMomentumScroll() {
		if (Math.abs(velocityX) < 0.01) {
			carouselRef.style.scrollBehavior = 'smooth';
			return;
		}

		const currentScroll = carouselRef.scrollLeft;
		let newScroll = currentScroll - velocityX * 16;

		if (newScroll < 0) {
			newScroll = 0;
			velocityX = Math.abs(velocityX) * 0.2;
		} else if (newScroll > maxScroll) {
			newScroll = maxScroll;
			velocityX = -Math.abs(velocityX) * 0.2;
		}

		carouselRef.scrollLeft = newScroll;
		velocityX *= 0.95;

		animationFrameId = requestAnimationFrame(applyMomentumScroll);
	}

	function checkMobile() {
		return window.innerWidth < 1024;
	}

	function calculateGrid() {
		if (checkMobile()) {
			itemWidth = 240;
			itemHeight = 140;
		} else {
			itemWidth = 240;
			itemHeight = 140;
		}
		if (containerRef) {
			containerHeight = itemHeight * rows;
		}
	}

	function setupScrollBehavior() {
		if (!carouselRef) return;
		maxScroll = carouselRef.scrollWidth - carouselRef.clientWidth;

		carouselRef.removeEventListener('mousedown', handleDragStart);
		carouselRef.removeEventListener('touchstart', handleDragStart);

		if (!isVerticalView) {
			carouselRef.addEventListener('mousedown', handleDragStart);
			carouselRef.addEventListener('touchstart', handleDragStart, { passive: true });
			carouselRef.style.cursor = 'grab';
		} else {
			carouselRef.style.cursor = 'default';
		}
	}

	async function toggleView() {
		isFading = true;
		await new Promise((resolve) => setTimeout(resolve, FADE_DURATION / 2));

		if (checkMobile()) {
			itemWidth = 240;
			itemHeight = 140;
		} else {
			itemWidth = 240;
			itemHeight = 140;
		}

		isVerticalView = !isVerticalView;

		if (animationFrameId) {
			cancelAnimationFrame(animationFrameId);
		}

		if (carouselRef) {
			carouselRef.style.scrollBehavior = 'auto';
			carouselRef.scrollLeft = 0;
			carouselRef.scrollTop = 0;
			leftOverscroll.set(0);
			rightOverscroll.set(0);
		}

		calculateGrid();
		setupScrollBehavior();

		await new Promise((resolve) => setTimeout(resolve, FADE_DURATION / 2));
		isFading = false;

		requestAnimationFrame(() => {
			lazyLoadImages();
			if (carouselRef) {
				carouselRef.style.scrollBehavior = 'smooth';
			}
		});
	}

	function lazyLoadImages(forceLoad = false) {
		if (containerRef) {
			const images = containerRef.querySelectorAll('img[data-src]');
			const containerRect = containerRef.getBoundingClientRect();

			images.forEach((img) => {
				const imgRect = img.getBoundingClientRect();
				if (forceLoad || (imgRect.left < containerRect.right + 200 && imgRect.right > containerRect.left - 200)) {
					if (img.dataset.src && img.src !== img.dataset.src) {
						img.src = img.dataset.src;
						img.removeAttribute('data-src');
					}
				}
			});
		}
	}

	function forceLoadVisibleImages() {
		lazyLoadImages(true);
	}

	function handleScroll() {
		requestAnimationFrame(lazyLoadImages);
	}

	function handleResize() {
		calculateGrid();
		setupScrollBehavior();
		lazyLoadImages();
	}

	function getKey(anime) {
		return `${anime.id || anime.mal_id}-${anime.current_episode || anime.num_episodes_watched}`;
	}

	onMount(() => {
		if (checkMobile()) {
			itemWidth = 240;
			itemHeight = 140;
		}

		calculateGrid();
		setupScrollBehavior();
		initializeEventListeners();
		window.addEventListener('resize', handleResize);
		carouselRef?.addEventListener('scroll', handleScroll, { passive: true });

		// Set initial height without animation
		if (isVerticalView) {
			containerHeightTweened.set(containerHeight, { duration: 0 });
		} else {
			containerHeightTweened.set(containerHeight / 1.5, { duration: 0 });
		}

		setTimeout(() => {
			initialRender = false;
		}, 0);

		return () => {
			if (animationFrameId) {
				cancelAnimationFrame(animationFrameId);
			}
			window.removeEventListener('resize', handleResize);
			carouselRef?.removeEventListener('scroll', handleScroll);

			if (listenersInitialized) {
				window.removeEventListener('mousemove', handleDragMove);
				window.removeEventListener('mouseup', handleDragEnd);
				window.removeEventListener('touchmove', handleDragMove);
				window.removeEventListener('touchend', handleDragEnd);
			}
		};
	});

	afterUpdate(() => {
		if (carouselRef) {
			setupScrollBehavior();
		}
		forceLoadVisibleImages();
	});

	$: if (continueWatching) {
		setTimeout(forceLoadVisibleImages, 0);
	}
</script>

<section class="relative z-10 {isLoggedIn ? 'mb-12' : 'mb-0'} mt-4 sm:mt-12 md:mt-4">
	<div class="flex items-center justify-between pl-4 pr-4 mb-2 sm:mb-6 md:mb-6 md:pr-8">
		<h2 class="{isLoggedIn ? '' : 'mb-6 md:mb-4'} text-2xl font-bold text-white/80 md:text-3xl">Kontynuuj oglądanie</h2>
		{#if isLoggedIn}
			<button on:click={toggleView} class="p-2 text-white transition-colors bg-gray-700 rounded-full cursor-pointer hover:bg-gray-600 md:mb-4">
				{#if isVerticalView}
					<ChevronUp size={24} />
				{:else}
					<ChevronDown size={24} />
				{/if}
			</button>
		{/if}
	</div>

	<div class="{!initialRender ? 'transition-[height] duration-400' : ''} relative overflow-visible" style="height: {$containerHeightTweened}px">
		<div class="relative w-full h-full">
			{#if !isFading}
				<div class="absolute inset-0" transition:fade|local={{ duration: initialRender ? 0 : FADE_DURATION }}>
					<div class="carousel-container" class:is-vertical={isVerticalView} bind:this={containerRef}>
						<div class="carousel-content" class:vertical={isVerticalView} bind:this={carouselRef} style="transform: translate3d({!isVerticalView ? $leftOverscroll - $rightOverscroll : 0}px, 0, 0);">
							<div class="carousel-slides">
								{#each continueWatching as anime (getKey(anime))}
									<div class="carousel-slide">
										<ContinueWatching {preferRomaji} episode={anime} {isDragging} {provider} />
									</div>
								{/each}
							</div>
						</div>
					</div>
				</div>
			{/if}
		</div>

		{#if !isLoggedIn}
			<div class="absolute inset-0 z-50 flex items-center justify-center -m-8 overflow-hidden rounded-lg">
				<div class="absolute inset-0 bg-gray-900/50 backdrop-blur-xs" aria-hidden="true" />
				<div class="relative z-10 -mt-10 w-full max-w-[85%] sm:max-w-[28rem]">
					<div
						class="mx-auto max-w-[20rem] rounded-xl border border-white/15 bg-slate-800/60 p-5 text-center backdrop-blur-[2px] transition duration-200 hover:-translate-y-2 sm:mt-8 sm:max-w-[24rem] sm:rounded-2xl sm:p-8"
					>
						<h2 class="mb-2 text-lg font-bold text-white sm:mb-4 sm:text-xl">Kontynuuj odkąd skończyłeś!</h2>
						<p class="mb-5 text-sm text-gray-200/90 sm:mb-8 sm:text-base">Zaloguj się, aby śledzić swój postęp oglądania i kontynuować od miejsca, w którym skończyłeś</p>

						<button
							on:click={handleLoginPrompt}
							class="inline-flex w-full items-center justify-center rounded-full bg-[#ee8585] px-5 py-2.5 text-sm font-bold text-gray-900 transition duration-200 hover:-translate-y-0.5 hover:cursor-pointer hover:bg-[#8ec3f4] active:translate-y-0 sm:px-6 sm:py-3 sm:text-base"
						>
							<svg
								xmlns="http://www.w3.org/2000/svg"
								viewBox="0 0 24 24"
								fill="none"
								stroke="currentColor"
								stroke-width="2"
								stroke-linecap="round"
								stroke-linejoin="round"
								class="mr-1.5 h-3.5 w-3.5 sm:mr-3 sm:h-5 sm:w-5"
								aria-hidden="true"
							>
								<polygon points="5 3 19 12 5 21 5 3" />
							</svg>
							Zaloguj się, aby kontynuować
						</button>
					</div>
				</div>
			</div>
		{/if}
	</div>
</section>

<UserProfileModal bind:open={showUserModal} user={null} onClose={handleCloseModal} />

<style>
	.carousel-container {
		width: 100%;
		height: calc(100% + 40px);
		margin: -20px 0;
		overflow-x: hidden;
		overflow-y: visible;
		-webkit-overflow-scrolling: touch;
		cursor: default;
		touch-action: pan-y pan-x;
		transition: all 400ms cubic-bezier(0.4, 0, 0.2, 1);
		opacity: 1;
	}

	.carousel-container.is-vertical {
		overflow-y: auto;
		overflow-x: hidden;
		touch-action: pan-y;
	}

	.carousel-content {
		display: flex;
		transition: transform 0.2s ease-out;
		overflow-x: auto;
		overflow-y: visible;
		scrollbar-width: none;
		-ms-overflow-style: none;
		-webkit-overflow-scrolling: touch;
		cursor: grab;
		user-select: none;
		will-change: transform;
		touch-action: pan-y pan-x;
		width: 100%;
	}

	.carousel-content:active {
		cursor: grabbing;
	}

	.carousel-content::-webkit-scrollbar {
		display: none;
	}

	.carousel-content.vertical {
		flex-wrap: wrap;
		gap: 14px;
		padding: 14px;
		justify-content: center;
		align-items: flex-start;
		cursor: default;
		overflow-x: hidden;
		overflow-y: visible;
		touch-action: pan-y;
		transform: none !important;
	}

	.carousel-slides {
		display: flex;
		gap: 14px;
		padding: 20px 14px;
		min-width: min-content;
		width: max-content;
		transition: all 400ms cubic-bezier(0.4, 0, 0.2, 1);
	}

	.vertical .carousel-slides {
		width: 100%;
		flex-wrap: wrap;
		justify-content: center;
	}

	.carousel-slide {
		flex: 0 0 auto;
		transition: all 400ms cubic-bezier(0.4, 0, 0.2, 1);
	}

	@media (min-width: 640px) {
		.carousel-slides {
			gap: 10px;
			padding: 0 10px;
		}

		.carousel-container.is-vertical {
			scrollbar-width: thin;
			scrollbar-color: rgba(255, 255, 255, 0.2) transparent;
		}

		.carousel-container.is-vertical::-webkit-scrollbar {
			display: block;
			width: 6px;
		}

		.carousel-container.is-vertical::-webkit-scrollbar-track {
			background: transparent;
		}

		.carousel-container.is-vertical::-webkit-scrollbar-thumb {
			background-color: rgba(255, 255, 255, 0.2);
			border-radius: 3px;
		}

		.carousel-container.is-vertical::-webkit-scrollbar-thumb:hover {
			background-color: rgba(255, 255, 255, 0.3);
		}
	}

	@media (min-width: 768px) {
		.carousel-slides {
			gap: 14px;
			padding: 0 14px;
		}
	}

	.duration-400 {
		transition-duration: 400ms;
	}

	.vertical .carousel-slide {
		width: calc(50% - 7px);
	}

	@media (min-width: 450px) {
		.vertical .carousel-slide {
			width: auto;
		}
	}
</style>
